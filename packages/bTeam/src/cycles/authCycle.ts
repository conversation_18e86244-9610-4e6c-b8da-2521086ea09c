import xs from 'xstream/index';
import { <PERSON>uff<PERSON> } from 'buffer';
import sampleCombine from 'xstream/extra/sampleCombine';
import { loginFailed, loginSuccess, tryLogin } from '../slices/authSlice';
import { clearAllErrors } from '../slices/gridMessageSlice';
import { tryLoginFailed, tryLoginSuccess } from '../slices/generalSlice';

export const logIn = sources => {
  const state$ = sources.STATE;

  const request$ = sources.ACTION.filter(
    action => action.type === tryLogin.type)
    .compose(sampleCombine())
    .map(([action]) => {
      const baseUrl = action.payload?.baseUrl;
      const userName = action.payload?.userName;
      const password = action.payload?.password;

      return {
        url: `${baseUrl}/api/UserLogins`,
        category: 'login',
        method: 'POST',
        headers: {
          accept: 'application/json',
          'Content-Type': 'application/json',
          'X-Mismatch-App': 'stats',
        },
        send: Buffer.from(
          JSON.stringify({
            USR_UserName: userName,
            USR_UserPassword: password,
          }),
        ),
      }
    });

  const response$ = sources.HTTP.select('login')
    .map(response => response.replaceError(err => xs.of(err)))
    .flatten()
    .filter(response => response.status === 200);

  const loginSuccessAction$ = xs
    .combine(response$)
    .map(([response]) => loginSuccess(response?.body));

  const loginSuccessLoadingAction$ = xs.combine(response$)
    .map(([response]) => tryLoginSuccess(response?.body));


  const clearErrorsAction$ = xs
    .combine(response$)
    .map(() => clearAllErrors());

  return {
    ACTION: xs.merge(loginSuccessAction$, clearErrorsAction$, loginSuccessLoadingAction$),
    HTTP: request$,
  };
};

export const logInFailed = sources => {
  const response$ = sources.HTTP.select('login')
    .map(response => response.replaceError(err => xs.of(err)))
    .flatten()
    .filter(response => response.status !== 200);

  const action$ = xs.combine(response$).map(arr => loginFailed(arr));
  const action1$ = xs.combine(response$).map(arr => tryLoginFailed(arr));

  return {
    ACTION: xs.merge(action$, action1$),
  };
};

// export const forgotPassword = sources => {
//   const request$ = sources.ACTION.filter(
//     action => action.type === ActionTypes.FORGOT_PASSWORD,
//   )
//     .compose(sampleCombine())
//     .map(([action]) => ({
//       url: `${config.API_BASE_URL}/auth/send-reset-password-email`,
//       category: 'forgotPassword',
//       method: 'POST',
//       headers: {
//         accept: 'application/json',
//         'Content-Type': 'application/json',
//       },
//       send: Buffer.from(
//         JSON.stringify({
//           email: action.payload,
//         }),
//       ),
//     }));

//   const response$ = sources.HTTP.select('forgotPassword')
//     .map(response => response.replaceError(err => xs.of(err)))
//     .flatten()
//     .filter(response => response.status === 200);

//   const action$ = xs
//     .combine(response$)
//     .map(response => actions.forgotPasswordSuccess(response?.[0]?.text));

//   return {
//     ACTION: action$,
//     HTTP: request$,
//   };
// };

// export const forgotPasswordFailed = sources => {
//   const response$ = sources.HTTP.select('forgotPassword')
//     .map(response => response.replaceError(err => xs.of(err)))
//     .flatten()
//     .filter(response => response.status !== 200);

//   const action$ = xs
//     .combine(response$)
//     .map(arr => actions.forgotPasswordFailed(arr));
//   return {
//     ACTION: action$,
//   };
// };
