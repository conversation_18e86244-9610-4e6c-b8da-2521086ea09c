import React from "react";
import { StyleSheet, View } from "react-native";
import { type Theme, useThemeAwareObject, CustomText } from "b-ui-lib";

type Props = {
  onSubmit: (formData: { username: string; password: string }) => void;
  errorMessage?: string;
  clearLogInErrorMessageAction: () => void;
};

const ContactsScreen: React.FC = ({}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      <CustomText>Contacts</CustomText>
    </View>
  );
};

export default ContactsScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
  });

  return { styles, color };
};
