import React, { Dispatch, SetStateAction, useRef, useState } from "react";
import {
  ActivityIndicator,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  TextInput,
  useWindowDimensions,
  View,
} from "react-native";
import BottomSheet from "@gorhom/bottom-sheet";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import Collapsible from "react-native-collapsible";
import RenderHtml from "react-native-render-html";
import { TEST_IDS } from "../../constants/testIds";

// Components
import {
  ComposeMessageInput,
  CustomText,
  FONT_SIZES,
  IconButton,
  Input,
  SPACING,
  Theme,
  useThemeAwareObject,
} from "b-ui-lib";
import { UserEmailAddresses, UserUniboxes } from "../../types/userMails";
import SelectRecipientsBottomSheet from "../toBeMovedToUILib/SelectRecipientsBottomSheet";
import AttachmentsSection from "./AttachmentsSection";

// Type for attachment objects that include both GUID and name
type AttachmentItem = {
  FLN_Guid: string;
  documentName: string;
};

type Props = {
  userUniboxes: UserUniboxes;
  userEmailAdresses: UserEmailAddresses;
  selectedRecipientEmailId: string;
  setSelectedRecipientEmailId: Dispatch<SetStateAction<string>>;
  emailToSend: string;
  handleEmailChange: (string) => void;
  ccEmail: string;
  handleEmailCcChange: (string) => void;
  bccEmail: string;
  handleEmailBccChange: (string) => void;
  subject: string;
  setSubject: Dispatch<SetStateAction<string>>;
  emailContent: string;
  setEmailContent: Dispatch<SetStateAction<string>>;
  isEmailValid: boolean;
  isCcEmailValid: boolean;
  isBccEmailValid: boolean;
  removeToRecipient: (string) => void;
  removeCcRecipient: (string) => void;
  removeBccRecipient: (string) => void;
  toRecipients: string[];
  ccRecipients: string[];
  bccRecipients: string[];
  messageBody: string;
  messageAttachments: AttachmentItem[];
  handleRemoveAttachment: (index: number) => void;
  sendMessageLoading: boolean;
  sendDraftMessageLoading: boolean;
};

const BOTTOM_SHEET_MENU_STATES = {
  addComment: "addComment",
  selectRecipient: "selectRecipient",
};
type BottomSheetMenuState =
  (typeof BOTTOM_SHEET_MENU_STATES)[keyof typeof BOTTOM_SHEET_MENU_STATES];

const ComposeMessageScreen = ({
  userUniboxes,
  userEmailAdresses,
  selectedRecipientEmailId,
  setSelectedRecipientEmailId,
  emailToSend,
  handleEmailChange,
  ccEmail,
  handleEmailCcChange,
  bccEmail,
  handleEmailBccChange,
  subject,
  setSubject,
  emailContent,
  setEmailContent,
  isEmailValid,
  isCcEmailValid,
  isBccEmailValid,
  removeToRecipient,
  removeCcRecipient,
  removeBccRecipient,
  toRecipients,
  ccRecipients,
  bccRecipients,
  messageBody,
  messageAttachments,
  handleRemoveAttachment,
  sendMessageLoading,
  sendDraftMessageLoading,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const { width } = useWindowDimensions();

  const [bottomSheetMenuState, setBottomSheetMenuState] =
    useState<BottomSheetMenuState>(BOTTOM_SHEET_MENU_STATES.addComment);
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState<boolean>(false);
  const [isCollapsed, setIsCollapsed] = useState<boolean>(true); // Collapsible state

  const bottomSheetRef = useRef<BottomSheet>(null);
  const inputRef1 = useRef<TextInput>(null);
  const inputRef2 = useRef<TextInput>(null);
  const inputRef3 = useRef<TextInput>(null);
  const inputRef4 = useRef<TextInput>(null);
  const inputRef5 = useRef<TextInput>(null);

  const handleArrowPress = () => {
    setIsBottomSheetOpen(!isBottomSheetOpen);
    bottomSheetRef.current?.expand();
  };

  const handleCloseBottomSheet = () => {
    setIsBottomSheetOpen(!isBottomSheetOpen);
    bottomSheetRef.current?.close();
  };

  const handleSheetChanges = (index: number) => {
    // -1 means closed in @gorhom/bottom-sheet
    if (index === -1) {
      setIsBottomSheetOpen(false);
      setBottomSheetMenuState(BOTTOM_SHEET_MENU_STATES.addComment);
      return;
    }
    // Any non-negative index means open/expanded at some snap point
    setIsBottomSheetOpen(true);
  };

  const handleBackPress = () => {
    if (bottomSheetMenuState === BOTTOM_SHEET_MENU_STATES.selectRecipient) {
      setBottomSheetMenuState(BOTTOM_SHEET_MENU_STATES.addComment);
      return true;
    }

    if (bottomSheetMenuState === BOTTOM_SHEET_MENU_STATES.addComment) {
      bottomSheetRef.current?.close();
      return true;
    }

    return false;
  };

  const EMAIL_CONTENT_NUMBER_OF_LINES = 10;

  return (
    <ScrollView
      style={styles.scrollContainer}
      contentContainerStyle={{
        flexGrow: 1,
      }}
    >
      <Pressable
        style={{
          flex: 1,
          backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
        }}
        onPress={() => {
          inputRef1.current?.blur();
          inputRef2.current?.blur();
          inputRef3.current?.blur();
          inputRef4.current?.blur();
          inputRef5.current?.blur();
          bottomSheetRef.current?.close();
        }}
      >
        <GestureHandlerRootView style={styles.container}>
          {(sendMessageLoading || sendDraftMessageLoading) && (
            <View
              style={
                (styles.absoluteLoader,
                {
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                  gap: 15,
                })
              }
            >
              <CustomText>
                {sendDraftMessageLoading ? "Saving..." : "Sending..."}
              </CustomText>
              <ActivityIndicator color={color.MESSAGE_FLAG} size="large" />
            </View>
          )}
          <ComposeMessageInput
            testID={TEST_IDS.composeFieldFrom}
            selectedEmail={
              userUniboxes.byId[selectedRecipientEmailId]?.value || ""
            }
            isBottomSheetOpen={isBottomSheetOpen}
            handleArrowPress={handleArrowPress}
            title="From:"
          />
          <View
            style={[
              styles.inputWrapper,
              {
                flexDirection: "row",
                alignItems: "center",
                borderWidth: 1,
                borderColor: isEmailValid
                  ? color.MESSAGE_ITEM__BACKGROUND
                  : color.ERROR,
                borderRadius: SPACING.SIX,
                padding: 4, // add some padding so the chips don't stick to the edges
              },
            ]}
          >
            <CustomText style={[styles.toText, { flex: 1 }]}>To:</CustomText>

            <View
              style={{
                flexDirection: "row",
                flexWrap: "wrap",
                alignItems: "center",
                flex: 10,
              }}
            >
              {toRecipients.map((email, index) => (
                <View key={index} style={styles.chip}>
                  <CustomText style={styles.chipText}>{email}</CustomText>
                  <IconButton
                    name="x"
                    size={12}
                    color={color.TEXT_DEFAULT}
                    onPress={() => removeToRecipient(index)}
                  />
                </View>
              ))}

              <TextInput
                testID={TEST_IDS.composeFieldTo}
                ref={inputRef1}
                keyboardType="email-address"
                value={emailToSend}
                onChangeText={handleEmailChange}
                style={{
                  color: color.MESSAGE_FLAG,
                  fontSize: FONT_SIZES.TWELVE,
                  flexShrink: 1, // allow the input to shrink when there isn’t enough space
                  minWidth: 100, // ensure a minimum width for the input
                  padding: 4,
                }}
                onFocus={() => bottomSheetRef.current?.close()}
                onBlur={() => {
                  if (emailToSend.trim() !== "") {
                    handleEmailChange(emailToSend + ",");
                  }
                }}
              />
            </View>

            <IconButton
              testID={TEST_IDS.composeExpandFieldsButton}
              name={!isCollapsed ? "chevron-up" : "chevron-down"}
              size={16}
              color={color.TEXT_DIMMED}
              onPress={() => setIsCollapsed(!isCollapsed)}
              style={{ flex: 1 }}
            />
          </View>

          {!isEmailValid && (
            <CustomText
              testID={TEST_IDS.composeFieldFromErrorMessage}
              style={[styles.errorText, { color: color.ERROR }]}
            >
              Please enter a valid email address.
            </CustomText>
          )}

          <View>
            <Collapsible collapsed={isCollapsed}>
              <View
                style={[
                  styles.inputWrapper,
                  {
                    flexDirection: "row",
                    alignItems: "center",
                    borderWidth: 1,
                    borderColor: isCcEmailValid
                      ? color.MESSAGE_ITEM__BACKGROUND
                      : color.ERROR,
                  },
                ]}
              >
                <CustomText style={[styles.toText, { flex: 1 }]}>
                  Cc:
                </CustomText>

                <View
                  style={{
                    flexDirection: "row",
                    flexWrap: "wrap",
                    alignItems: "center",
                    flex: 11,
                  }}
                >
                  <View style={styles.chipsContainer}>
                    {ccRecipients.map((email, index) => (
                      <View key={index} style={styles.chip}>
                        <CustomText style={styles.chipText}>{email}</CustomText>
                        <IconButton
                          name="x"
                          size={12}
                          color={color.TEXT_DEFAULT}
                          onPress={() => removeCcRecipient(index)}
                        />
                      </View>
                    ))}
                  </View>

                  <TextInput
                    testID={TEST_IDS.composeFieldCc}
                    ref={inputRef4}
                    keyboardType="email-address"
                    value={ccEmail}
                    onChangeText={handleEmailCcChange}
                    style={{
                      color: color.MESSAGE_FLAG,
                      fontSize: FONT_SIZES.TWELVE,
                      flex: 1,
                    }}
                    onFocus={() => bottomSheetRef.current?.close()}
                    onBlur={() => {
                      if (ccEmail.trim() !== "") {
                        handleEmailCcChange(ccEmail + ",");
                      }
                    }}
                  />
                </View>
              </View>

              {!isCcEmailValid && (
                <CustomText
                  testID={TEST_IDS.composeFieldCcErrorMessage}
                  style={[styles.errorText, { color: color.ERROR }]}
                >
                  Please enter a valid Cc email address.
                </CustomText>
              )}

              <View
                style={[
                  styles.inputWrapper,
                  {
                    flexDirection: "row",
                    alignItems: "center",
                    borderWidth: 1,
                    borderColor: isBccEmailValid
                      ? color.MESSAGE_ITEM__BACKGROUND
                      : color.ERROR,
                  },
                ]}
              >
                <CustomText style={[styles.toText, { flex: 1 }]}>
                  Bcc:
                </CustomText>

                <View
                  style={{
                    flexDirection: "row",
                    flexWrap: "wrap",
                    alignItems: "center",
                    flex: 11,
                  }}
                >
                  <View style={styles.chipsContainer}>
                    {bccRecipients.map((email, index) => (
                      <View key={index} style={styles.chip}>
                        <CustomText style={styles.chipText}>{email}</CustomText>
                        <IconButton
                          name="x"
                          size={12}
                          color={color.TEXT_DEFAULT}
                          onPress={() => removeBccRecipient(index)}
                        />
                      </View>
                    ))}
                  </View>

                  <TextInput
                    testID={TEST_IDS.composeFieldBcc}
                    ref={inputRef5}
                    keyboardType="email-address"
                    value={bccEmail}
                    onChangeText={handleEmailBccChange}
                    style={{
                      color: color.MESSAGE_FLAG,
                      fontSize: FONT_SIZES.TWELVE,
                      flex: 1,
                    }}
                    onFocus={() => bottomSheetRef.current?.close()}
                    onBlur={() => {
                      if (bccEmail.trim() !== "") {
                        handleEmailBccChange(bccEmail + ",");
                      }
                    }}
                  />
                </View>
              </View>

              {!isBccEmailValid && (
                <CustomText
                  testID={TEST_IDS.composeFieldCcErrorMessage}
                  style={[styles.errorText, { color: color.ERROR }]}
                >
                  Please enter a valid Bcc email address.
                </CustomText>
              )}
            </Collapsible>
          </View>

          <Input
            testID={TEST_IDS.composeFieldSubject}
            inputRef={inputRef2}
            value={subject}
            placeholder="Subject"
            onChangeText={setSubject}
            inputStyle={{
              fontSize: 20,
              fontWeight: "700",
            }}
            containerStyle={{
              paddingVertical: Platform.OS === "android" ? 0 : SPACING.XS,
              paddingHorizontal: Platform.OS === "android" ? 5 : SPACING.S,
              borderColor: color.MESSAGE_ITEM__BACKGROUND,
            }}
            onFocus={() => bottomSheetRef.current?.close()}
          />

          <Input
            testID={TEST_IDS.composeFieldBody}
            inputRef={inputRef3}
            value={emailContent}
            placeholder="Enter text here"
            onChangeText={setEmailContent}
            inputStyle={{
              textAlignVertical: "top",
              minHeight:
                Platform.OS === "ios"
                  ? EMAIL_CONTENT_NUMBER_OF_LINES * 15
                  : undefined,
            }}
            numberOfLines={EMAIL_CONTENT_NUMBER_OF_LINES}
            multiline
            onFocus={() => bottomSheetRef.current?.close()}
            containerStyle={{
              borderColor: color.MESSAGE_ITEM__BACKGROUND,
              paddingHorizontal: Platform.OS === "android" ? 5 : SPACING.S,
            }}
          />

          <AttachmentsSection
            messageAttachments={messageAttachments}
            handleRemoveAttachment={handleRemoveAttachment}
          />

          <RenderHtml
            contentWidth={width}
            source={{ html: messageBody }}
            enableExperimentalMarginCollapsing={true}
            defaultTextProps={{
              style: styles.htmlTextStyle,
            }}
          />

          <SelectRecipientsBottomSheet
            bottomSheetRef={bottomSheetRef}
            index={-1}
            enablePanDownToClose
            handleSheetChanges={handleSheetChanges}
            handleBackPress={handleBackPress}
            recipientsEmails={userUniboxes.allIds.map(
              (id) => userUniboxes.byId[id]
            )}
            handleAddButton={handleCloseBottomSheet}
            selectedRecipientEmailId={selectedRecipientEmailId}
            handleSelectRecipientEmail={(emailId: string) =>
              emailId === selectedRecipientEmailId
                ? setSelectedRecipientEmailId("")
                : setSelectedRecipientEmailId(emailId)
            }
            handleCloseBottomSheet={handleCloseBottomSheet}
            handleBackdropPress={handleCloseBottomSheet}
            titleBoldText="Choose"
            titleNormalText="Email"
          />
        </GestureHandlerRootView>
      </Pressable>
    </ScrollView>
  );
};

export default ComposeMessageScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    scrollContainer: {
      flexGrow: 1,
      backgroundColor: color.BACKGROUND,
    },
    container: {
      flex: 1,
      padding: SPACING.S,
    },
    bottomSheetStyle: {
      backgroundColor: color.BACKGROUND,
    },
    bottomSheetIndicatorStyle: {
      backgroundColor: color.MESSAGE_FLAG,
    },
    bottomSheetScrollView: {
      backgroundColor: color.BACKGROUND,
    },
    toText: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.TEXT_DEFAULT,
      marginRight: 10,
    },
    inputWrapper: {
      marginTop: 10,
      paddingHorizontal: SPACING.TEN,
    },
    errorText: {
      fontSize: 12,
    },
    absoluteLoader: {
      position: "absolute",
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      zIndex: 100,
    },
    chipsContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      alignItems: "center",
    },
    chip: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: color.PRESSABLE,
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 4,
      marginRight: 4,
      marginBottom: 4,
    },
    chipText: {
      fontSize: FONT_SIZES.TWELVE,
      marginRight: 4,
    },
    htmlTextStyle: {
      color: color.TEXT_DEFAULT,
    },
  });

  return { styles, color };
};
