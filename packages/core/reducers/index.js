import { combineReducers } from "redux";

//reducers
import { questionReducerCreator } from "bAudit/src/slices/questionsSlice";
import authReducer from "bAudit/src/slices/authSlice";
import generalSlice from "bAudit/src/slices/generalSlice";
import commentsReducer from "bSuite/src/slices/commentsSlice";
import approvalsReducer from "bSuite/src/slices/approvalsSlice";
import bSparePartsAuthReducer from "../../bSpareParts/src/reducers/authReducer";
import dbConnectionDataReducer from "../../bSpareParts/src/reducers/dbConnectionDataReducer";
import dbDownloadedDataReducer from "../../bSpareParts/src/reducers/dbDownloadedDataReducer";
import inventorySparePartsReducer from "../../bSpareParts/src/reducers/inventorySparePartsReducer";
import consumptionSparePartsReducer from "../../bSpareParts/src/reducers/consumptionSparePartsReducer";
import deliverySparePartsReducer from "../../bSpareParts/src/reducers/deliverySparePartsReducer";
import messageSlice from "../../bTeam/src/slices/messageSlice";
import gridMessageSlice from "../../bTeam/src/slices/gridMessageSlice";
import authSlice from "../../bTeam/src/slices/authSlice";
import folderSlice from "../../bTeam/src/slices/folderSlice";
import bTeamGeneralSlice from "../../bTeam/src/slices/generalSlice";
import bTeamUsers from "../../bTeam/src/slices/usersMailDomainSlice";
import bTeamSearchSuggestionsSlice from "../../bTeam/src/slices/searchSuggestionsSlice";
import bTeamSearchFiltersSlice from "../../bTeam/src/slices/searchFiltersSlice";
import bTeamAttachmentsSlice from "../../bTeam/src/slices/attachmentsSlice";
import notificationsSlice from "../../bTeam/src/slices/notificationsSlice";
import commentsSlice from "../../bTeam/src/slices/commentsSlice";
import generalNotificationsSlice from "../../bTeam/src/slices/generalNotificationsSlice";
import casesSlice from "../../bTeam/src/slices/casesSlice";
import metadataSlice from "../../bTeam/src/slices/metadataSlice";
import caseProjectsSlice from "../../bTeam/src/slices/caseProjectsSlice";
import caseStatusesSlice from "../../bTeam/src/slices/caseStatusesSlice";
import casePriotitiesSlice from "../../bTeam/src/slices/casePrioritiesSlice";
import caseTypesSlice from "../../bTeam/src/slices/caseTypesSlice";
import bTeamCriteriaSlice from "../../bTeam/src/slices/criteriaSlice";
import bSignatureAuthSlice from "../../bSignature/src/slices/authSlice";
import bSignatureDocumentsSlice from "../../bSignature/src/slices/documentsSlice";
import bSignatureDashboardSlice from "../../bSignature/src/slices/dashboardSlice";
import bSignaturePendingDocumentsSlice from "../../bSignature/src/slices/pendingDocumentsSlice";
import bSignatureCompletedDocumentsSlice from "../../bSignature/src/slices/completedDocumentsSlice";
import bSignatureDocumentDetailsSlice from "../../bSignature/src/slices/documentDetailsSlice";

const bTeamReducersRoot = {
  bTeamGeneralNotificationSlice: generalNotificationsSlice,
};

const bSparePartsReducersRoot = {
  auth: bSparePartsAuthReducer,
};

const bSparePartsReducersPersist = {
  dbConnectionData: dbConnectionDataReducer,
  dbDownloadedData: dbDownloadedDataReducer,
  inventorySpareParts: inventorySparePartsReducer,
  consumptionSpareParts: consumptionSparePartsReducer,
  deliverySpareParts: deliverySparePartsReducer,
};

const root = combineReducers({
  authReducer,
  generalSlice,
  approvalsReducer,
  ...bSparePartsReducersRoot,
  ...bTeamReducersRoot,
  bTeamGeneralSlice,
  bTeamAttachmentsSlice,
  bSignatureDocumentsSlice,
  bSignatureDashboardSlice,
  bSignaturePendingDocumentsSlice,
  bSignatureCompletedDocumentsSlice,
  bSignatureDocumentDetailsSlice,
  bTeamSearchFiltersSlice,
});

const bAuditInitialState = {
  space: "normal",
  ratings: {
    allIds: [],
  },
  ratingRows: {
    allIds: [],
  },
  inspectionTypes: {
    allIds: [],
  },
  vessels: {
    allIds: [],
  },
  checklists: {
    allIds: [],
  },
  questionCategories: {
    allIds: [],
  },
  questions: {
    allIds: [],
  },
  inspections: {
    allIds: [],
  },
  questionAnswers: {
    allIds: [],
  },
  attachments: {
    allIds: [],
  },
  resultItems: {
    1: {
      id: "1",
      description: "N/A (Not applicable)",
    },
    2: {
      id: "2",
      description: "No (deficiency)",
    },
    3: {
      id: "3",
      description: "Yes (found OK)",
    },
    4: {
      id: "4",
      description: "N/S (Not seen)",
    },

    allIds: ["1", "2", "3", "4"],
  },
  pendingInspecionId: null,
  downloadedFiles: [],
  progress: { allIds: [] },
  uploadProgress: {
    allIds: [],
  },
  networkPreference: "all",
  currentNetwork: null,
  isConnectedToNetwork: null,
  cloudStorageFolder: "",
  cloudStorageToken: "",
  esbCompanyGuid: "",
  token: null,
  device: null,
};

const bAuditRecycleInitialState = {
  ...bAuditInitialState,
  space: "recycle",
};

const questionReducer = questionReducerCreator(bAuditInitialState);
const recycleReducer = questionReducerCreator(bAuditRecycleInitialState);

export const {
  setRatings,
  setVessels,
  setChecklists,
  setInspectionTypes,
  setQuestionAnswers,
  setNewInspection,
  setAttachments,
  deleteAttachment,
  startSession,
  setOffset,
  finishSession,
  moveInspections,
  editNewInspection,
  setUploadStatus,
  setDownloadedFiles,
  deleteInspections,
  setProgress,
  setFilePaths,
  setSessionId,
  setNetworkPreference,
  setCredentials,
  deleteEsbCompanyGuid,
  setToken,
  setDevice,
} = questionReducer.actions;

const persist = combineReducers({
  questionReducer: questionReducer.reducer,
  commentsReducer,
  recycleReducer: recycleReducer.reducer,
  ...bSparePartsReducersPersist,
  bTeamAuth: authSlice,
  messageSlice,
  gridMessageSlice,
  bTeamNotificationSlice: notificationsSlice,
  bTeamCommentsSlice: commentsSlice,
  bTeamSearchSuggestionsSlice,
  folder: folderSlice,
  bTeamUsers,
  bTeamCasesSlice: casesSlice,
  bTeamMetadataSlice: metadataSlice,
  bTeamCaseProjectsSlice: caseProjectsSlice,
  bTeamCaseStatusesSlice: caseStatusesSlice,
  bTeamCasePriotitiesSlice: casePriotitiesSlice,
  bTeamCaseTypesSlice: caseTypesSlice,
  bSignatureAuthSlice,
  bTeamCriteriaSlice,
});

const reducers = combineReducers({
  root,
  persist,
});

export default reducers;
